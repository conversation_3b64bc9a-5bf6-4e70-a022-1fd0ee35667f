
<?php $__env->startSection('head'); ?>
    <?php $__env->startPush('css'); ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/plugins/bootstrap-iconpicker/css/bootstrap-iconpicker.min.css')); ?>" />
    <?php $__env->stopPush(); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Edit Plan'),
        // 'buttons' => [
        //     [
        //         'name' => __('Back'),
        //         'url' => route('admin.customer.index'),
        //     ],
        // ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row ">
        <div class="col-lg-5 mt-5">
            <strong><?php echo e(__('Customer Plan Edit')); ?></strong>
            
        </div>
        <div class="col-lg-7 mt-5">
            <form class="ajaxform_instant_reload" action="<?php echo e(route('admin.customer.plan.update', ['id' => $user->id])); ?>"
                method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PATCH'); ?>
                <div class="card">
                    <div class="card-body">
                        <?php
                            $planData = json_decode($user->plan, true) ?? [];
                            $access_bot_builder = $planData['access_bot_builder'] ?? 'false';
                            $access_agent_list = $planData['access_agent_list'] ?? 'false';
                        ?>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Monthly Messages Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[messages_limit]"
                                    value="<?php echo e($planData['messages_limit'] ?? '-1'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Contacts Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[contact_limit]"
                                    value="<?php echo e($planData['contact_limit'] ?? '-1'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Device Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[device_limit]"
                                    value="<?php echo e($planData['device_limit'] ?? '-1'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Template Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[template_limit]"
                                    value="<?php echo e($planData['template_limit'] ?? '-1'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('App Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[apps_limit]"
                                    value="<?php echo e($planData['apps_limit'] ?? '-1'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Agent Limit')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[agent_limit]"
                                    value="<?php echo e($planData['agent_limit'] ?? '0'); ?>" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Access Agent List')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_agent_list]">
                                    <option value="true" <?php echo e($access_agent_list == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?></option>
                                    <option value="false" <?php echo e($access_agent_list == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Chatbot')); ?> <small class="text-muted">(Automatic
                                    Reply)</small> </label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[chatbot]">
                                    <option value="true"
                                        <?php echo e(($planData['chatbot'] ?? 'false') == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?></option>
                                    <option value="false"
                                        <?php echo e(($planData['chatbot'] ?? 'false') == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Bulk Message')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[bulk_message]">
                                    <option value="true"
                                        <?php echo e(!isset($planData['bulk_message']) || $planData['bulk_message'] == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?>

                                    </option>
                                    <option value="false"
                                        <?php echo e(isset($planData['bulk_message']) && $planData['bulk_message'] == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?>

                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Schedule Message')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[schedule_message]">
                                    <option value="true"
                                        <?php echo e(!isset($planData['schedule_message']) || $planData['schedule_message'] == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?>

                                    </option>
                                    <option value="false"
                                        <?php echo e(isset($planData['schedule_message']) && $planData['schedule_message'] == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?>

                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Chat List Access')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_chat_list]">
                                    <option value="true"
                                        <?php echo e(!isset($planData['access_chat_list']) || $planData['access_chat_list'] == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?>

                                    </option>
                                    <option value="false"
                                        <?php echo e(isset($planData['access_chat_list']) && $planData['access_chat_list'] == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?>

                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Group List Access')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_group_list]">
                                    <option value="true"
                                        <?php echo e(!isset($planData['access_group_list']) || $planData['access_group_list'] == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?>

                                    </option>
                                    <option value="false"
                                        <?php echo e(isset($planData['access_group_list']) && $planData['access_group_list'] == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?>

                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Bot Builder Access')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_bot_builder]">
                                    <option value="true" <?php echo e($access_bot_builder == 'true' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Enabled')); ?></option>
                                    <option value="false" <?php echo e($access_bot_builder == 'false' ? 'selected' : ''); ?>>
                                        <?php echo e(__('Disabled')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Select Plan')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="user_plan_id" required="">
                                    <option value="">Select Plan</option>
                                    <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        
                                        <option value="<?php echo e($plan->id); ?>"
                                            <?php echo e(!isset($user->plan_id) || $user->plan_id == $plan->id ? 'selected' : '1'); ?>>
                                            <?php echo e($plan->title); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Select Reseller')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="reseller_id">
                                    <option value="">Select Reseller</option>
                                    <?php $__currentLoopData = $resellers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reseller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($reseller->id); ?>"
                                            <?php echo e($user->reseller_id == $reseller->id ? 'selected' : ''); ?>>
                                            <?php echo e($reseller->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12"><?php echo e(__('Expiry On')); ?></label>
                            <div class="col-lg-12">
                                <input type="date" name="will_expire"
                                    value="<?php echo e(!isset($user->will_expire) ? now()->format('Y-m-d') : $user->will_expire); ?>"
                                    required="" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Current Balance')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="balance" value="<?php echo e($user->balance); ?>" required=""
                                    class="form-control" readonly>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Per Message Rate')); ?></label>
                            <div class="col-lg-12">
                                
                                
                                <input type="number" name="business_initiated" value="<?php echo e($user->business_initiated); ?>"
                                    required="" class="form-control" min="0.11" step="0.01">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Select Credit Or Debit')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plancre_deb">
                                    <option value="credit"><?php echo e(__('Credit')); ?></option>
                                    <option value="debit"><?php echo e(__('Debit')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Enter Balance')); ?></label>
                            <div class="col-lg-12">
                                <input type="number" name="balance_update" placeholder="Enter Balance"
                                    class="form-control">
                            </div>
                        </div>
                        
                        <div class="from-group row mt-3">
                            <div class="col-lg-12">
                                <button class="btn btn-neutral submit-button btn-md float-left">
                                    <?php echo e(__('Update')); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/plugins/bootstrap-iconpicker/js/iconset/fontawesome5-3-1.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/plugins/bootstrap-iconpicker/js/bootstrap-iconpicker.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/admin/plan-edit.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/customers/plan_edit.blade.php ENDPATH**/ ?>