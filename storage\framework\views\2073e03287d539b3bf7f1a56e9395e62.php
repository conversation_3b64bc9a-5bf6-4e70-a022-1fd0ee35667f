
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Reseller Users'),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-30">
                        <div class="col-lg-6">
                            <h4><?php echo e(__('Resellers User List')); ?></h4>
                        </div>
                        <div class="col-lg-6">
                        </div>
                    </div>
                    <br>
                    <div class="card-action-filter">
                        <div class="table-responsive custom-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th class="text-center"><?php echo e(__('Name')); ?></th>
                                        <th class="text-center"><?php echo e(__('Email')); ?></th>
                                        <th class="text-center"><?php echo e(__('Mobile')); ?></th>
                                        <th class="text-center"><?php echo e(__('Plan')); ?></th>
                                        <th class="text-center"><?php echo e(__('Order')); ?></th>
                                        <th class="text-center"><?php echo e(__('Expire Date')); ?></th>
                                        <th class="text-center"><?php echo e(__('Status')); ?></th>
                                        <th class="text-center"><?php echo e(__('Created At')); ?></th>
                                        <th class="text-center"><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        
                                        <tr>
                                            <td class="text-center">
                                                <?php echo e($row->name); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e(Str::limit($row->email ?? '', 50)); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e($row->phone ?? ''); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e($row->subscription->title ?? ''); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e(number_format($row->orders_count)); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e(\Carbon\Carbon::parse($row->will_expire)->format('d M Y')); ?>

                                            </td>
                                            <td class="text-center">
                                                <span
                                                    class="badge badge-<?php echo e($row->will_expire <= now() ? 'danger' : 'success'); ?>">
                                                    <?php echo e($row->will_expire <= now() ? 'Expire' : 'Active'); ?>

                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <?php echo e($row->created_at->format('d M y')); ?>

                                            </td>
                                            <td class="text-center">
                                                <div class="dropdown">
                                                    <a class="btn btn-sm btn-icon-only text-light" href="#"
                                                        role="button" data-toggle="dropdown" aria-haspopup="true"
                                                        aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </a>
                                                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-arrow">
                                                        <a class="dropdown-item"
                                                            href="<?php echo e(route('admin.resellerUserOrder', ['user_id' => $row->id, 'reseller_id' => $row->reseller_id])); ?>"><?php echo e(__('View Order')); ?></a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/admin/reseller_user.blade.php ENDPATH**/ ?>