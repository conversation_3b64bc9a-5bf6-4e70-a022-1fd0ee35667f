
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Admins'),
        'buttons' => [
            [
                'name' => '<i class="fa fa-plus"></i>&nbsp' . __('Create a admin'),
                'url' => route('admin.admin.create'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-30">
                        <div class="col-lg-6">
                            <h4><?php echo e(__('Admins')); ?></h4>
                        </div>
                        <div class="col-lg-6">
                        </div>
                    </div>
                    <br>
                    <div class="card-action-filter">
                        <div class="table-responsive custom-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th class="text-center"><?php echo e(__('Name')); ?></th>
                                        <th class="text-center"><?php echo e(__('Email')); ?></th>
                                        <th class="text-center"><?php echo e(__('Credit')); ?></th>
                                        <th class="text-center"><?php echo e(__('Status')); ?></th>
                                        <th class="text-center"><?php echo e(__('Role')); ?></th>
                                        <th class="text-center"><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    
                                    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        
                                        <tr>
                                            <td class="text-center">
                                                <?php echo e($row->name); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e($row->email); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php echo e($row->credit); ?>

                                            </td>
                                            <td class="text-center">
                                                <?php if($row->status == 1): ?>
                                                    <span class="badge badge-success"><?php echo e(__('Active')); ?></span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger"><?php echo e(__('Deactive')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php $__currentLoopData = $row->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span class="badge badge-primary"><?php echo e($r->name); ?></span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </td>
                                            <td class="text-center">
                                                <div class="dropdown">
                                                    <a class="btn btn-sm btn-icon-only text-light" href="#"
                                                        role="button" data-toggle="dropdown" aria-haspopup="true"
                                                        aria-expanded="false">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </a>
                                                    <div class="dropdown-menu dropdown-menu-right dropdown-menu-arrow">
                                                        <a class="dropdown-item"
                                                            href="<?php echo e(route('admin.resellerUser', $row->id)); ?>"><?php echo e(__('View User')); ?></a>
                                                        <a class="dropdown-item"
                                                            href="<?php echo e(route('admin.admin.show', $row->id)); ?>"><?php echo e(__('View Order')); ?></a>
                                                        <a class="dropdown-item"
                                                            href="<?php echo e(route('admin.add_credit', $row->id)); ?>"><?php echo e(__('Manage Credit')); ?></a>
                                                        <a class="dropdown-item"
                                                            href="<?php echo e(route('admin.admin.edit', $row->id)); ?>"><?php echo e(__('Edit')); ?></a>
                                                        <a class="dropdown-item delete-confirm" href="#"
                                                            data-action="<?php echo e(route('admin.admin.destroy', $row->id)); ?>"><?php echo e(__('Delete')); ?></a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/admin/index.blade.php ENDPATH**/ ?>