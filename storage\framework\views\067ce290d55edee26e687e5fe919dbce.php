
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Edit Admin'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.admin.index'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-lg-5 mt-5">
            <strong><?php echo e(__('Credit Add')); ?></strong>
            <p><?php echo e(__('update credit to user')); ?></p>
        </div>
        <div class="col-lg-7 mt-5">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="<?php echo e(route('admin.credit_update', $user->id)); ?>" class="ajaxform">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <div class="pt-20">
                            <div class="form-group">
                                <label><?php echo e(__('Credit')); ?></label>
                                <select name="credit" class="form-control">
                                    <option value="1" selected><?php echo e(__('Credit')); ?></option>
                                    <option value="2"><?php echo e(__('Debit')); ?></option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="user_credit"><?php echo e(__('Enter Credit')); ?></label>
                                <input type="number" placeholder="Enter Credit" name="user_credit" class="form-control"
                                    id="user_credit" required="">
                            </div>
                            <div class="form-group">
                                <label for="note"><?php echo e(__('Note')); ?></label>
                                <input type="text" placeholder="Enter Note" name="note" class="form-control"
                                    id="note" required="">
                            </div>

                        </div>
                </div>
                <div class="card-footer">
                    <div class="btn-publish">
                        <button type="submit" class="btn btn-neutral submit-button"><i class="fa fa-save"></i>
                            <?php echo e(__('Save')); ?></button>
                    </div>
                </div>
            </div>

        </div>

        </form>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/admin/addcredit.blade.php ENDPATH**/ ?>