@extends('layouts.main.app')
@section('head')
    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/plugins/bootstrap-iconpicker/css/bootstrap-iconpicker.min.css') }}" />
    @endpush
    @include('layouts.main.headersection', [
        'title' => __('Edit Plan'),
        // 'buttons' => [
        //     [
        //         'name' => __('Back'),
        //         'url' => route('admin.customer.index'),
        //     ],
        // ],
    ])
@endsection
@section('content')
    <div class="row ">
        <div class="col-lg-5 mt-5">
            <strong>{{ __('Customer Plan Edit') }}</strong>
            {{-- <p>{{ __('Edit customer plan') }}</p> --}}
        </div>
        <div class="col-lg-7 mt-5">
            <form class="ajaxform_instant_reload" action="{{ route('admin.customer.plan.update', ['id' => $user->id]) }}"
                method="POST">
                @csrf
                @method('PATCH')
                <div class="card">
                    <div class="card-body">
                        @php
                            $planData = json_decode($user->plan, true) ?? [];
                            $access_bot_builder = $planData['access_bot_builder'] ?? 'false';
                            $access_agent_list = $planData['access_agent_list'] ?? 'false';
                        @endphp
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Monthly Messages Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[messages_limit]"
                                    value="{{ $planData['messages_limit'] ?? '-1' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Contacts Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[contact_limit]"
                                    value="{{ $planData['contact_limit'] ?? '-1' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Device Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[device_limit]"
                                    value="{{ $planData['device_limit'] ?? '-1' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Template Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[template_limit]"
                                    value="{{ $planData['template_limit'] ?? '-1' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('App Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[apps_limit]"
                                    value="{{ $planData['apps_limit'] ?? '-1' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Agent Limit') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="plan_data[agent_limit]"
                                    value="{{ $planData['agent_limit'] ?? '0' }}" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Access Agent List') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_agent_list]">
                                    <option value="true" {{ $access_agent_list == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}</option>
                                    <option value="false" {{ $access_agent_list == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Chatbot') }} <small class="text-muted">(Automatic
                                    Reply)</small> </label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[chatbot]">
                                    <option value="true"
                                        {{ ($planData['chatbot'] ?? 'false') == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}</option>
                                    <option value="false"
                                        {{ ($planData['chatbot'] ?? 'false') == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Bulk Message') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[bulk_message]">
                                    <option value="true"
                                        {{ !isset($planData['bulk_message']) || $planData['bulk_message'] == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}
                                    </option>
                                    <option value="false"
                                        {{ isset($planData['bulk_message']) && $planData['bulk_message'] == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Schedule Message') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[schedule_message]">
                                    <option value="true"
                                        {{ !isset($planData['schedule_message']) || $planData['schedule_message'] == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}
                                    </option>
                                    <option value="false"
                                        {{ isset($planData['schedule_message']) && $planData['schedule_message'] == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Chat List Access') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_chat_list]">
                                    <option value="true"
                                        {{ !isset($planData['access_chat_list']) || $planData['access_chat_list'] == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}
                                    </option>
                                    <option value="false"
                                        {{ isset($planData['access_chat_list']) && $planData['access_chat_list'] == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Group List Access') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_group_list]">
                                    <option value="true"
                                        {{ !isset($planData['access_group_list']) || $planData['access_group_list'] == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}
                                    </option>
                                    <option value="false"
                                        {{ isset($planData['access_group_list']) && $planData['access_group_list'] == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Bot Builder Access') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plan_data[access_bot_builder]">
                                    <option value="true" {{ $access_bot_builder == 'true' ? 'selected' : '' }}>
                                        {{ __('Enabled') }}</option>
                                    <option value="false" {{ $access_bot_builder == 'false' ? 'selected' : '' }}>
                                        {{ __('Disabled') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Select Plan') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="user_plan_id" required="">
                                    <option value="">Select Plan</option>
                                    @foreach ($plans as $plan)
                                        {{-- <option value="{{ $plan->id }}"
                                            {{ $user->plan_id == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->title }}
                                        </option> --}}
                                        <option value="{{ $plan->id }}"
                                            {{ !isset($user->plan_id) || $user->plan_id == $plan->id ? 'selected' : '1' }}>
                                            {{ $plan->title }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Select Reseller') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="reseller_id">
                                    <option value="">Select Reseller</option>
                                    @foreach ($resellers as $reseller)
                                        <option value="{{ $reseller->id }}"
                                            {{ $user->reseller_id == $reseller->id ? 'selected' : '' }}>
                                            {{ $reseller->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2" style="display: none;">
                            <label class="col-lg-12">{{ __('Expiry On') }}</label>
                            <div class="col-lg-12">
                                <input type="date" name="will_expire"
                                    value="{{ !isset($user->will_expire) ? now()->format('Y-m-d') : $user->will_expire }}"
                                    required="" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Current Balance') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="balance" value="{{ $user->balance }}" required=""
                                    class="form-control" readonly>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Per Message Rate') }}</label>
                            <div class="col-lg-12">
                                {{-- <input type="number" name="business_initiated" value="{{ $user->business_initiated }}"
                                    required="" class="form-control" min="0.11"> --}}
                                {{-- message rate not less then 0.11 --}}
                                <input type="number" name="business_initiated" value="{{ $user->business_initiated }}"
                                    required="" class="form-control" min="0.11" step="0.01">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Select Credit Or Debit') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="plancre_deb">
                                    <option value="credit">{{ __('Credit') }}</option>
                                    <option value="debit">{{ __('Debit') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Enter Balance') }}</label>
                            <div class="col-lg-12">
                                <input type="number" name="balance_update" placeholder="Enter Balance"
                                    class="form-control">
                            </div>
                        </div>
                        {{-- @if (Auth::id() == 1)
                            <div class="from-group row mt-2">
                                <label class="col-lg-12">{{ __('Manual Expiry Date') }} <small class="text-muted">(Leave
                                        blank if not applicable)</small> </label>
                                <div class="col-lg-12">
                                    <input type="date" name="manual_expire" class="form-control">
                                </div>
                            </div>
                        @endif --}}
                        <div class="from-group row mt-3">
                            <div class="col-lg-12">
                                <button class="btn btn-neutral submit-button btn-md float-left">
                                    {{ __('Update') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/plugins/bootstrap-iconpicker/js/iconset/fontawesome5-3-1.min.js') }}"></script>
    <script src="{{ asset('assets/plugins/bootstrap-iconpicker/js/bootstrap-iconpicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/pages/admin/plan-edit.js') }}"></script>
@endpush
