<!-- Nav items -->
<ul class="navbar-nav">
    @if (api_plan() && api_plan()->title == 'Api')
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/dashboard*') ? 'active' : '' }}"
                href="{{ route('user.dashboard.index') }}">
                <i class="fi fi-rs-apps"></i>
                <span class="nav-link-text">{{ __('Dashboard') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/device*') ? 'active' : '' }}" href="{{ route('user.account.index') }}">
                <i class="fi-rs-devices"></i>
                <span class="nav-link-text">{{ __('My Devices') }}</span>
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/apps*') ? 'active' : '' }}" href="{{ route('user.apps.index') }}">
                <i class="fi fi-rs-apps-add"></i>
                <span class="nav-link-text">{{ __('My Api') }}</span>
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/template*') ? 'active' : '' }}" href="{{ url('user/template') }}">
                <i class="fi fi-rs-form"></i>
                <span class="nav-link-text">{{ __('My Templates') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/response*') ? 'active' : '' }}" href="{{ url('user/response') }}">
                <i class="fi-rs-message-code"></i>
                <span class="nav-link-text">{{ __('Response') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/report*') ? 'active' : '' }}" href="{{ url('user/report') }}">
                <i class="fi fi-rs-document-signed"></i>
                <span class="nav-link-text">{{ __('Report') }}</span>
            </a>
        </li>
    @else
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/dashboard*') ? 'active' : '' }}"
                href="{{ route('user.dashboard.index') }}">
                <i class="fi fi-rs-apps"></i>
                <span class="nav-link-text">{{ __('Dashboard') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/account*') ? 'active' : '' }}"
                href="{{ route('user.account.index') }}">
                <i class="fi-rs-devices"></i>
                <span class="nav-link-text">{{ __('My Account') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/sent-text-message*') ? 'active' : '' }}"
                href="{{ url('user/sent-text-message') }}">
                <i class="fi fi-rs-paper-plane"></i>
                <span class="nav-link-text">{{ __('Send Message') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/dynamic-sent-text-message*') ? 'active' : '' }}"
                href="{{ url('user/dynamic-sent-text-message') }}">
                <i class="fi fi-rs-paper-plane"></i>
                <span class="nav-link-text">{{ __('Send Dynamic Message') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/chatbot*') ? 'active' : '' }}"
                href="{{ route('user.chatbot.index') }}">
                <i class="fi fi-rs-comment-alt"></i>
                <span class="nav-link-text">{{ __('Auto Reply') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/apps*') ? 'active' : '' }}" href="{{ route('user.apps.index') }}">
                <i class="fi fi-rs-apps-add"></i>
                <span class="nav-link-text">{{ __('My Api') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/contact*') ? 'active' : '' }}"
                href="{{ route('user.contact.index') }}">
                <i class="fi  fi-rs-address-book"></i>
                <span class="nav-link-text">{{ __('Contacts Book') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/template*') ? 'active' : '' }}" href="{{ url('user/template') }}">
                <i class="fi fi-rs-form"></i>
                <span class="nav-link-text">{{ __('My Templates') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/response*') ? 'active' : '' }}" href="{{ url('user/response') }}">
                <i class="fi-rs-message-code"></i>
                <span class="nav-link-text">{{ __('Response') }}</span>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/report*') ? 'active' : '' }}" href="{{ url('user/report') }}">
                <i class="fi fi-rs-document-signed"></i>
                <span class="nav-link-text">{{ __('Report') }}</span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link {{ Request::is('user/greetings*') ? 'active' : '' }}"
                href="{{ url('user/greetings') }}">
                <i class="fi fi-rs-gifts"></i>
                <span class="nav-link-text">{{ __('Greetings') }}</span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link {{ Request::is('user/tag*') ? 'active' : '' }}" href="{{ url('user/tag') }}">
                <i class="fi fi-rs-tags"></i>
                <span class="nav-link-text">{{ __('Manage Tag') }}</span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link {{ Request::is('user/facebook_leads*') ? 'active' : '' }}"
                href="{{ url('user/facebook_leads') }}">
                <i class="fi fi-rs-shopping-basket"></i>
                <span class="nav-link-text">{{ __('Integration') }}</span>
            </a>
        </li>
        <li class="nav-item ">
            <a class="nav-link {{ Request::is('user/drip_campaign*') ? 'active' : '' }}"
                href="{{ route('user.drip_campaign.index') }}">
                <i class="fi fi-rs-raindrops"></i>
                <span class="nav-link-text">{{ __('Drip Campaign') }}</span>
            </a>
        </li>
        {{-- <li class="nav-item">
            <a class="nav-link {{ Request::is('user/bot_builder*') ? 'active' : '' }}"
                href="{{ route('user.bot_builder.index') }}">
                <i class="fas fa-robot"></i>
                <span class="nav-link-text">{{ __('Bot Builder') }}</span>
                <span class="badge badge-pill badge-danger ml-2">New</span>
            </a>
        </li> --}}
    @endif
</ul>


<!-- Divider -->
<hr class="my-3 mt-6">
<!-- Heading -->
<h6 class="navbar-heading p-0 text-muted">{{ __('Settings') }}</h6>
<!-- Navigation -->
<ul class="navbar-nav mb-md-3">
    @php
        $currentDomain = request()->getHttpHost();
        $allowedDomain = 'dmsv2.nxccontrols.in';
    @endphp
    {{-- agent and role hide temporary --}}
    {{-- <li class="nav-item">
        <a class="nav-link {{ Request::is('user/agent*') || Request::is('user/agent*') ? 'active' : '' }}"
            href="#agent-roles" data-toggle="collapse" role="button" aria-expanded="false" aria-controls="agent-roles">
            <i class="fi  fi-rs-shield-check"></i>
            <span class="nav-link-text">{{ __('Agent and Role') }}</span>
        </a>
        <div class="collapse" id="agent-roles">
            <ul class="nav nav-sm flex-column">
                <li class="nav-item">
                    <a href="{{ route('user.agent.index') }}" class="nav-link">{{ __('Agent') }}</a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('user.role.index') }}" class="nav-link">{{ __('Roles') }}</a>
                </li>
            </ul>
        </div>
    </li> --}}
    <li class="nav-item">
        <a class="nav-link {{ Request::is('user/agent*') ? 'active' : '' }}" href="{{ route('user.agent.index') }}">
            <i class="fi fi-rs-shield-check"></i>
            <span class="nav-link-text">{{ __('Agent') }}</span>
            <span class="badge badge-pill badge-danger ml-2">New</span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ Request::is('user/deletechat*') ? 'active' : '' }}"
            href="{{ url('/user/deletechat') }}">
            <i class="fi fi-rs-trash"></i>
            <span class="nav-link-text">{{ __('Delete Chat') }}</span>
        </a>
    </li>
    @if ($currentDomain == $allowedDomain)
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/subscription*') ? 'active' : '' }}"
                href="{{ url('/user/subscription') }}">
                <i class="fi fi-rs-rocket-lunch"></i>
                <span class="nav-link-text">{{ __('Subscription') }}</span>
            </a>
        </li>
    @else
        <li class="nav-item">
            <a class="nav-link {{ Request::is('user/subscriptions/log') ? 'active' : '' }}"
                href="{{ url('/user/subscriptions/log') }}">
                <i class="fi fi-rs-rocket-lunch"></i>
                <span class="nav-link-text">{{ __('Subscription Log') }}</span>
            </a>
        </li>
    @endif
    <li class="nav-item">
        <a class="nav-link {{ Request::is('user/support*') ? 'active' : '' }}" href="{{ url('/user/support') }}">
            <i class="fi fi-rs-headset"></i>
            <span class="nav-link-text">{{ __('Help & Support') }}</span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="{{ url('/user/profile') }}">
            <i class="fi fi-rs-settings"></i>
            <span class="nav-link-text">{{ __('Profile Settings') }}</span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link {{ Request::is('user/auth-key*') ? 'active' : '' }}" href="{{ url('/user/auth-key') }}">
            <i class="fi fi-rs-key"></i>
            <span class="nav-link-text">{{ __('Auth Key') }}</span>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link logout-button" href="#">
            <i class="fi fi-rs-log-out"></i>
            <span class="nav-link-text">{{ __('Logout') }}</span>
        </a>
    </li>
</ul>
